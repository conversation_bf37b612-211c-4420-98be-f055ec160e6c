import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import { UserProfileHeader } from "@/components/dashboard/UserProfileHeader";
import { Toaster } from "@/components/ui/toaster";
import { useScreenSize } from "@/hooks/use-mobile";
import { useEffect, useState } from "react";
import { Outlet } from "react-router-dom";

export function DashboardLayout() {
  const screenSize = useScreenSize();

  // Initialize sidebar state from localStorage or defaults
  const [sidebarOpen, setSidebarOpen] = useState(() => {
    const saved = localStorage.getItem('sidebar-open');
    if (saved !== null) {
      return JSON.parse(saved);
    }
    // Default values based on screen size
    return screenSize === 'desktop';
  });

  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Save sidebar state to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('sidebar-open', JSON.stringify(sidebarOpen));
  }, [sidebarOpen]);

  // Only handle mobile menu state based on screen size, not sidebar state
  useEffect(() => {
    if (screenSize === 'mobile') {
      setMobileMenuOpen(false); // Close mobile menu when switching to mobile
    }
  }, [screenSize]);

  const handleToggleSidebar = () => {
    if (screenSize === 'mobile') {
      setMobileMenuOpen(!mobileMenuOpen);
    } else {
      setSidebarOpen(!sidebarOpen);
    }
  };

  return (
    <div className="h-screen w-screen flex bg-background overflow-hidden fixed inset-0">
      {/* Sidebar */}
      <DashboardSidebar
        open={sidebarOpen}
        setOpen={setSidebarOpen}
        mobileMenuOpen={mobileMenuOpen}
        setMobileMenuOpen={setMobileMenuOpen}
        screenSize={screenSize}
      />

      {/* Mobile overlay */}
      {screenSize === 'mobile' && mobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* Main content */}
      <div className="flex flex-col flex-1 min-w-0 overflow-hidden h-full">
        {/* Top navigation */}
        <UserProfileHeader toggleSidebar={handleToggleSidebar} />

        {/* Main content area */}
        <main className="flex-1 overflow-auto custom-scrollbar">
          <div className="p-4 sm:p-6">
            <PageTransition>
              <Outlet />
            </PageTransition>
          </div>
        </main>
      </div>

      {/* Global UI elements */}
      <Toaster />
    </div>
  );
}
