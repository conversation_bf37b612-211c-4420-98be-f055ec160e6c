import { LocationSelector } from "@/components/location-selector/LocationSelector";
import { OrganizationSelector } from "@/components/organization/OrganizationSelector";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { useAuth } from "@/hooks/useAuth";
import { useNotifications } from "@/hooks/useNotifications";
import { cn } from "@/lib/utils";
import {
  Bell,
  Building,
  Check,
  LogOut,
  Menu,
  Search,
  Settings,
  User,
} from "lucide-react";
import { useLocation, useNavigate } from "react-router-dom";

interface UserProfileHeaderProps {
  toggleSidebar: () => void;
}

export function UserProfileHeader({ toggleSidebar }: UserProfileHeaderProps) {
  const { user, signOut, organization } = useAuth();
  const { unreadCount, notifications, markAsRead, markAllAsRead } =
    useNotifications();
  const navigate = useNavigate();
  const location = useLocation();

  const userInitials =
    user?.user_metadata?.first_name && user?.user_metadata?.last_name
      ? `${user.user_metadata.first_name[0]}${user.user_metadata.last_name[0]}`
      : user?.email?.substring(0, 2).toUpperCase() || "U";

  // Hide org switcher if on organizations page and in All Organizations mode
  const hideOrgSwitcher =
    organization?.id === "system-admin-all-orgs" &&
    location.pathname.startsWith("/organizations");

  return (
    <header className="h-16 border-b border-border bg-background/95 backdrop-blur supports-backdrop-blur:bg-background/60 z-10 flex-shrink-0">
      <div className="flex h-full items-center justify-between px-4 gap-2 sm:gap-4 max-w-full overflow-hidden">
        {/* Left section - Sidebar toggle and search */}
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden flex-shrink-0 h-8 w-8 p-0 rounded-full"
            onClick={toggleSidebar}
          >
            <Menu className="h-4 w-4" />
            <span className="sr-only">Toggle sidebar</span>
          </Button>

          <div className="relative w-full max-w-md hidden sm:block">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search patients, appointments..."
              className="w-full bg-background pl-8 focus-visible:ring-primary"
            />
          </div>
        </div>

        {/* Center section - Organization and Location Selectors */}
        <div className="flex items-center gap-2 flex-shrink-0 md:flex">
          {!hideOrgSwitcher && <OrganizationSelector />}
          <LocationSelector />
        </div>

        {/* Right section - Theme toggle, notifications, user profile */}
        <div className="flex items-center gap-2 sm:gap-4 flex-shrink-0">
          <ThemeToggle />

          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="sm" className="relative h-8 w-8 p-0 rounded-full flex-shrink-0">
                <Bell className="h-4 w-4" />
                {unreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 flex items-center justify-center min-w-[14px] h-[14px] text-[10px] font-bold bg-red-500 text-white rounded-full px-1">
                    {unreadCount > 9 ? "9+" : unreadCount}
                  </span>
                )}
                <span className="sr-only">Notifications ({unreadCount})</span>
              </Button>
            </PopoverTrigger>
            <PopoverContent
              className="w-80 p-0"
              align="end"
              sideOffset={6}
              forceMount
            >
              <div className="flex items-center justify-between p-4 border-b">
                <h3 className="font-medium">Notifications</h3>
                {unreadCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 text-xs"
                    onClick={(e) => {
                      e.stopPropagation();
                      markAllAsRead();
                    }}
                  >
                    <Check className="mr-1 h-3 w-3" />
                    Mark all as read
                  </Button>
                )}
              </div>
              <div className="max-h-80 overflow-y-auto">
                {notifications.length === 0 ? (
                  <div className="p-4 text-center text-sm text-muted-foreground">
                    No notifications
                  </div>
                ) : (
                  <div className="flex flex-col">
                    {notifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={cn(
                          "p-4 border-b last:border-b-0 cursor-pointer hover:bg-accent/50 transition-colors",
                          !notification.read && "bg-accent/20",
                        )}
                        onClick={(e) => {
                          e.stopPropagation();
                          markAsRead(notification.id);
                        }}
                      >
                        <div className="flex items-start gap-2">
                          <div
                            className={cn(
                              "w-2 h-2 mt-1.5 rounded-full",
                              notification.type === "info" && "bg-blue-500",
                              notification.type === "success" && "bg-green-500",
                              notification.type === "warning" &&
                                "bg-yellow-500",
                              notification.type === "error" && "bg-red-500",
                            )}
                          />
                          <div className="flex-1">
                            <p className="text-sm font-medium">
                              {notification.title}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {notification.message}
                            </p>
                            <p className="text-xs text-muted-foreground/70 mt-2">
                              {new Date(
                                notification.created_at,
                              ).toLocaleTimeString([], {
                                hour: "2-digit",
                                minute: "2-digit",
                              })}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </PopoverContent>
          </Popover>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-8 w-8 rounded-full flex-shrink-0">
                <Avatar className="h-8 w-8">
                  <AvatarImage
                    src={user?.user_metadata?.avatar_url || ""}
                    alt={user?.user_metadata?.full_name || "User"}
                  />
                  <AvatarFallback>{userInitials}</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {user?.user_metadata?.first_name
                      ? `${user.user_metadata.first_name} ${user.user_metadata.last_name || ''}`.trim()
                      : user?.email}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user?.email}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => navigate("/profile")}>
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => navigate("/organization")}>
                <Building className="mr-2 h-4 w-4" />
                <span>Organization</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => navigate("/settings")}>
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => signOut()}>
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
