import { useEffect, useRef, useState } from "react";
import { useLocation } from "react-router-dom";

interface PageTransitionProps {
  children: React.ReactNode;
}

export function PageTransition({ children }: PageTransitionProps) {
  const location = useLocation();
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [displayLocation, setDisplayLocation] = useState(location);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // If location changed, start transition
    if (location.pathname !== displayLocation.pathname) {
      setIsTransitioning(true);
      
      // Add exit animation class
      if (contentRef.current) {
        contentRef.current.classList.remove('page-transition-enter');
        contentRef.current.classList.add('page-transition-exit');
      }

      // After exit animation, update content and start enter animation
      const timer = setTimeout(() => {
        setDisplayLocation(location);
        
        if (contentRef.current) {
          contentRef.current.classList.remove('page-transition-exit');
          contentRef.current.classList.add('page-transition-enter');
        }

        // Clear transition state after enter animation
        const enterTimer = setTimeout(() => {
          setIsTransitioning(false);
          if (contentRef.current) {
            contentRef.current.classList.remove('page-transition-enter');
          }
        }, 200); // Match --duration-fast (0.2s)

        return () => clearTimeout(enterTimer);
      }, 200); // Match --duration-fast (0.2s)

      return () => clearTimeout(timer);
    }
  }, [location, displayLocation]);

  // Initial mount animation
  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.classList.add('page-transition-enter');
      
      const timer = setTimeout(() => {
        if (contentRef.current) {
          contentRef.current.classList.remove('page-transition-enter');
        }
      }, 200);

      return () => clearTimeout(timer);
    }
  }, []);

  return (
    <div className="page-transition-container">
      <div 
        ref={contentRef}
        className="page-transition-content"
        key={displayLocation.pathname}
      >
        {children}
      </div>
    </div>
  );
}
