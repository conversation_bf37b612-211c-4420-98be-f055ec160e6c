import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataPagination } from "@/components/ui/data-pagination";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell, TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { useAuth } from "@/hooks/useAuth";
import { usePagination } from "@/hooks/usePagination";
import { useUserRoles } from "@/hooks/useUserRoles";
import { supabase } from "@/lib/supabase";
import { TABLE_STORAGE_KEYS } from "@/lib/table-preferences";
import { Building, Building2, Calendar, Plus, Search, Users } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

// Enhanced organization interface with stats
interface OrganizationWithStats {
  id: string;
  name: string;
  type: string;
  settings: Record<string, unknown>;
  created_at: string | null;
  updated_at: string | null;
  parent_id: string | null;
  hierarchy_level: number | null;
  hierarchy_path: string | null;
  user_count: number;
  patient_count: number;
  user_role?: string;
  is_current?: boolean;
  children?: OrganizationWithStats[];
  subscription_tier?: string | null;
}

export function OrganizationsPage() {
  const { user, organization: currentOrg } = useAuth();
  const { isSystemAdmin } = useUserRoles();
  const navigate = useNavigate();

  const [organizations, setOrganizations] = useState<OrganizationWithStats[]>(
    [],
  );
  const [filteredOrganizations, setFilteredOrganizations] = useState<
    OrganizationWithStats[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [totalCount, setTotalCount] = useState(0);

  // Separate state for total stats
  const [totalStats, setTotalStats] = useState({
    totalOrganizations: 0,
    totalUsers: 0,
    totalPatients: 0,
    rootOrganizations: 0,
  });

  // Pagination setup with configurable page size and localStorage persistence
  const {
    currentPage,
    totalPages,
    offset,
    pageSize,
    itemsPerPage,
    goToPage,
    setPageSize,
  } = usePagination({
    totalCount,
    allowPageSizeChange: true,
    defaultPageSize: 10,
    storageKey: TABLE_STORAGE_KEYS.ORGANIZATIONS,
  });

  // Build hierarchical organization tree and sort properly
  const buildHierarchicalList = useCallback((orgs: OrganizationWithStats[]) => {
    // Create a map for quick lookup
    const orgMap = new Map(orgs.map(org => [org.id, { ...org, children: [] as OrganizationWithStats[] }]));

    // Build the tree structure
    const roots: OrganizationWithStats[] = [];

    orgs.forEach(org => {
      const orgWithChildren = orgMap.get(org.id)!;

      if (org.parent_id && orgMap.has(org.parent_id)) {
        // Add to parent's children
        const parent = orgMap.get(org.parent_id)!;
        parent.children!.push(orgWithChildren);
      } else {
        // This is a root organization
        roots.push(orgWithChildren);
      }
    });

    // Flatten the tree in hierarchical order
    const flattenHierarchy = (nodes: OrganizationWithStats[], level = 0): OrganizationWithStats[] => {
      const result: OrganizationWithStats[] = [];

      // Sort nodes at current level by name
      const sortedNodes = [...nodes].sort((a, b) => a.name.localeCompare(b.name));

      for (const node of sortedNodes) {
        // Add the node itself
        result.push({ ...node, hierarchy_level: level });

        // Add its children recursively
        if (node.children && node.children.length > 0) {
          result.push(...flattenHierarchy(node.children, level + 1));
        }
      }

      return result;
    };

    return flattenHierarchy(roots);
  }, []);

  // Fetch total stats (called once, not dependent on pagination)
  const fetchTotalStats = useCallback(async () => {
    if (!isSystemAdmin) return;

    try {
      // Get total organization count
      const { count: totalOrgsCount } = await supabase
        .from("organizations")
        .select("*", { count: "exact", head: true });

      // Get root organizations count
      const { count: rootOrgsCount } = await supabase
        .from("organizations")
        .select("*", { count: "exact", head: true })
        .is("parent_id", null);

      // Get total users across all organizations
      const { count: totalUsersCount } = await supabase
        .from("user_roles")
        .select("*", { count: "exact", head: true });

      // Get total patients across all organizations
      let totalPatientsCount = 0;
      try {
        const { count } = await supabase
          .from("patients")
          .select("*", { count: "exact", head: true });
        totalPatientsCount = count || 0;
      } catch {
        // Fallback if patients table doesn't exist
        totalPatientsCount = 0;
      }

      setTotalStats({
        totalOrganizations: totalOrgsCount || 0,
        totalUsers: totalUsersCount || 0,
        totalPatients: totalPatientsCount,
        rootOrganizations: rootOrgsCount || 0,
      });
    } catch (error) {
      console.error("Error fetching total stats:", error);
    }
  }, [isSystemAdmin]);

  // Fetch organizations with stats and pagination
  const fetchOrganizations = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      if (isSystemAdmin) {
        // System admins see all organizations
        // First get total count for pagination
        const { count: totalOrgsCount } = await supabase
          .from("organizations")
          .select("*", { count: "exact", head: true });

        // Always set the base total count first
        setTotalCount(totalOrgsCount || 0);

        // Then get paginated data with search filtering
        let query = supabase
          .from("organizations")
          .select("*", { count: "exact" })
          .order("name");

        // Apply search filter if present
        if (searchTerm.trim()) {
          query = query.or(`name.ilike.%${searchTerm}%`);
        }

        const { data: allOrgs, error: orgsError, count: filteredCount } = await query
          .range(offset, offset + itemsPerPage - 1);

        if (orgsError) throw orgsError;

        // Update total count with filtered count if searching
        if (searchTerm.trim()) {
          setTotalCount(filteredCount || 0);
        }

        // Get stats for each organization
        const orgsWithStats = await Promise.all(
          (allOrgs || []).map(async (org) => {
            // Get user count
            const { count: userCount } = await supabase
              .from("user_roles")
              .select("*", { count: "exact", head: true })
              .eq("organization_id", org.id);

            // Get patient count (if patients table exists)
            let patientCount = 0;
            try {
              const result = await supabase
                .from("patients")
                .select("*", { count: "exact", head: true })
                .eq("organization_id", org.id);
              patientCount = result.count || 0;
            } catch {
              // Fallback if patients table doesn't exist
              patientCount = 0;
            }

            return {
              ...org,
              settings: (org.settings as Record<string, unknown>) || {},
              parent_id: org.parent_id || null,
              hierarchy_level: org.hierarchy_level || null,
              hierarchy_path: org.hierarchy_path || null,
              user_count: userCount || 0,
              patient_count: patientCount,
              user_role: "system_admin",
              is_current: currentOrg?.id === org.id,
            };
          }),
        );

        const sortedOrgs = buildHierarchicalList(orgsWithStats);

        setOrganizations(sortedOrgs);
        setFilteredOrganizations(sortedOrgs);
      } else {
        // Regular users see only their organizations
        const { data, error } = await supabase
          .from("user_roles")
          .select(
            `
            role,
            organization:organizations (
              id, name, type, settings, created_at, updated_at, parent_id, hierarchy_level, hierarchy_path
            )
          `,
          )
          .eq("user_id", user.id);

        if (error) throw error;

        const rawData = data as unknown as Array<{
          role: string;
          organization: {
            id: string;
            name: string;
            type: string;
            settings: Record<string, unknown>;
            created_at: string;
            updated_at: string;
            parent_id: string | null;
            hierarchy_level: number | null;
            hierarchy_path: string | null;
          } | null;
        }>;

        const userOrgs = rawData.filter((item) => item.organization !== null);
        setTotalCount(userOrgs.length);

        // Apply pagination to user organizations
        const paginatedUserOrgs = userOrgs.slice(offset, offset + itemsPerPage);

        // Transform and get stats for user's organizations
        const userOrgsWithStats = await Promise.all(
          paginatedUserOrgs.map(async (item) => {
            const org = item.organization!;

            // Get user count
            const { count: userCount } = await supabase
              .from("user_roles")
              .select("*", { count: "exact", head: true })
              .eq("organization_id", org.id);

            // Get patient count
            let patientCount = 0;
            try {
              const result = await supabase
                .from("patients")
                .select("*", { count: "exact", head: true })
                .eq("organization_id", org.id);
              patientCount = result.count || 0;
            } catch {
              // Fallback if patients table doesn't exist
              patientCount = 0;
            }

            return {
              ...org,
              settings: (org.settings as Record<string, unknown>) || {},
              parent_id: org.parent_id || null,
              hierarchy_level: org.hierarchy_level || null,
              hierarchy_path: org.hierarchy_path || null,
              user_count: userCount || 0,
              patient_count: patientCount,
              user_role: item.role,
              is_current: currentOrg?.id === org.id,
            };
          }),
        );

        // Use the same hierarchical sorting for user organizations
        const sortedUserOrgs = buildHierarchicalList(userOrgsWithStats);

        setOrganizations(sortedUserOrgs);
        setFilteredOrganizations(sortedUserOrgs);
      }
    } catch (error) {
      console.error("Error fetching organizations:", error);
      toast.error("Failed to load organizations");
    } finally {
      setIsLoading(false);
    }
  }, [user, isSystemAdmin, currentOrg?.id, offset, itemsPerPage, searchTerm]);

  // Handle search changes and reset pagination
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    goToPage(1); // Reset to first page when search changes
  };

  // Set filtered organizations to match fetched organizations (filtering is done server-side)
  useEffect(() => {
    setFilteredOrganizations(organizations);
  }, [organizations]);

  useEffect(() => {
    if (user) {
      fetchOrganizations();
    }
  }, [fetchOrganizations, user]);

  // Fetch total stats on mount for system admins
  useEffect(() => {
    if (user && isSystemAdmin) {
      fetchTotalStats();
    }
  }, [fetchTotalStats, isSystemAdmin, user]);

  // Handle view organization details (click handler required by table, but navigation is handled elsewhere)
  const handleViewOrganization = (id: string) => {
    navigate(`/organizations/${id}`);
  };

  const handleCreateOrganization = () => {
    navigate("/setup/organization");
  };

  const pageTitle = isSystemAdmin ? "All Organizations" : "Your Organizations";
  const pageDescription = isSystemAdmin
    ? "Manage all organizations in the system"
    : "Choose an organization to work with or create a new one";

  // Calculate stats

  return (
    <div className="space-y-6 w-full">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold">{pageTitle}</h1>
          <p className="text-muted-foreground mt-1">{pageDescription}</p>
        </div>
        <Button onClick={handleCreateOrganization} className="w-full sm:w-auto">
          <Plus className="mr-2 h-4 w-4" />
          Create Organization
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Building2 className="h-4 w-4 text-muted-foreground" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Total Organizations</p>
                <p className="text-2xl font-bold">{totalStats.totalOrganizations}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Building className="h-4 w-4 text-muted-foreground" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Root Organizations</p>
                <p className="text-2xl font-bold">{totalStats.rootOrganizations}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-4 w-4 text-muted-foreground" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Total Users</p>
                <p className="text-2xl font-bold">{totalStats.totalUsers}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-4 w-4 text-muted-foreground" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Total Patients</p>
                <p className="text-2xl font-bold">{totalStats.totalPatients}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Organization Directory
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Search */}
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search organizations..."
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-9"
            />
          </div>

          {/* Table Content */}
          {isLoading ? (
        <div className="flex justify-center py-12">
          <div className="flex flex-col items-center gap-2">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            <p className="text-sm text-muted-foreground">
              Loading organizations...
            </p>
          </div>
        </div>
      ) : filteredOrganizations.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Organization</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Parent</TableHead>
                <TableHead>Users</TableHead>
                <TableHead>Patients</TableHead>
                <TableHead>Your Role</TableHead>
                <TableHead>Created</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredOrganizations.map((org) => {
                return (
                  <TableRow
                    key={org.id}
                    className={`cursor-pointer hover:bg-muted/50 ${org.is_current ? "bg-primary/5" : ""}`}
                    onClick={() => handleViewOrganization(org.id)}
                  >
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div style={{ marginLeft: `${(org.hierarchy_level || 0) * 16}px` }} className="flex items-center gap-3">
                          <Building2 className="h-8 w-8 text-muted-foreground" />
                          <div>
                            <div className="font-medium">{org.name}</div>
                            <div className="text-sm text-muted-foreground flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">{org.type}</Badge>
                              {org.subscription_tier && (
                                <Badge variant="outline" className="text-xs">{org.subscription_tier}</Badge>
                              )}
                              {org.is_current && (
                                <Badge variant="secondary" className="text-xs">Current</Badge>
                              )}
                              {org.hierarchy_level !== null && org.hierarchy_level > 0 && (
                                <Badge variant="outline" className="text-xs">L{org.hierarchy_level}</Badge>
                              )}
                              {org.parent_id && (
                                <span className="text-xs">↳ {organizations.find(o => o.id === org.parent_id)?.name || 'Unknown'}</span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{org.type}</Badge>
                    </TableCell>
                    <TableCell>
                      {org.parent_id ? (
                        <span className="text-sm text-muted-foreground">
                          {organizations.find(o => o.id === org.parent_id)?.name || 'Unknown'}
                        </span>
                      ) : (
                        <span className="text-sm text-muted-foreground">Root</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        {org.user_count}
                      </div>
                    </TableCell>
                    <TableCell>{org.patient_count}</TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          org.user_role === "system_admin"
                            ? "default"
                            : "secondary"
                        }
                      >
                        {org.user_role?.replace("_", " ")}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        {org.created_at ? new Date(org.created_at).toLocaleDateString() : 'N/A'}
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
      ) : (
        <div className="text-center py-12 border rounded-lg bg-muted/20">
          <Building2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">
            {searchTerm ? "No Organizations Found" : "No Organizations"}
          </h3>
          <p className="text-muted-foreground mb-6 max-w-md mx-auto">
            {searchTerm
              ? `No organizations found matching "${searchTerm}"`
              : "You don't have any organizations yet. Create your first organization to get started."}
          </p>
          {!searchTerm && (
            <Button onClick={handleCreateOrganization}>
              <Plus className="mr-2 h-4 w-4" /> Create Your First Organization
            </Button>
          )}
        </div>
      )}

          {/* Pagination - only show when not loading */}
          {!isLoading && (
            <DataPagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={goToPage}
              pageSize={pageSize}
              onPageSizeChange={setPageSize}
              totalCount={totalCount}
              showPageSizeSelector={true}
              showTotalCount={true}
              totalCountLabel="organization"
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
